<!DOCTYPE html>
<html>
<head>
    <title>自动跟进助手调试测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        .test-button:hover { background: #005a87; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>自动跟进助手调试测试</h1>
    
    <div class="test-section">
        <h2>测试按钮</h2>
        <button class="test-button" onclick="testClickButton()">测试 clickButton 函数</button>
        <button class="test-button" onclick="testWaitFunction()">测试 wait 函数</button>
        <button class="test-button" onclick="testHandleUpgradeDialog()">测试 handleUpgradeDialog 函数</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="test-section">
        <h2>模拟跟进按钮</h2>
        <button class="test-button" id="mockFollowButton">跟进</button>
        <button class="test-button" id="mockFollowButton2">跟进客户</button>
        <button class="test-button" id="mockFollowButton3">立即跟进</button>
    </div>
    
    <div class="test-section">
        <h2>日志输出</h2>
        <div id="logOutput" class="log">等待测试...</div>
    </div>

    <script>
        // 模拟控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : '';
            logOutput.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            addToLog(args.join(' '), 'info');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToLog(args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToLog(args.join(' '), 'warning');
            originalWarn.apply(console, args);
        };
        
        function clearLog() {
            document.getElementById('logOutput').innerHTML = '日志已清空\n';
        }
        
        // 测试函数
        async function testClickButton() {
            addToLog('开始测试 clickButton 函数', 'info');
            
            const mockButton = document.getElementById('mockFollowButton');
            if (!mockButton) {
                addToLog('错误：找不到模拟按钮', 'error');
                return;
            }
            
            try {
                // 模拟 clickButton 函数的简化版本
                console.log('[自动跟进助手] clickButton 开始执行');
                
                if (!mockButton) {
                    console.error('[自动跟进助手] 按钮为空');
                    throw new Error('按钮为空');
                }
                
                console.log('[自动跟进助手] 按钮文本:', mockButton.textContent?.substring(0, 20));
                
                // 直接点击
                mockButton.click();
                console.log('[自动跟进助手] 按钮点击完成');
                
                addToLog('clickButton 测试成功', 'success');
            } catch (error) {
                addToLog(`clickButton 测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testWaitFunction() {
            addToLog('开始测试 wait 函数', 'info');
            
            try {
                const startTime = Date.now();
                
                // 模拟 wait 函数
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                addToLog(`wait 函数测试成功，等待时间: ${duration}ms`, 'success');
            } catch (error) {
                addToLog(`wait 函数测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testHandleUpgradeDialog() {
            addToLog('开始测试 handleUpgradeDialog 函数', 'info');
            
            try {
                console.log('[自动跟进助手] handleUpgradeDialog 开始');
                
                // 模拟等待
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const dialog = document.querySelector('.el-dialog__wrapper');
                if (!dialog) {
                    console.log('[自动跟进助手] 没有找到对话框');
                } else {
                    console.log('[自动跟进助手] 找到对话框');
                }
                
                console.log('[自动跟进助手] handleUpgradeDialog 完成');
                addToLog('handleUpgradeDialog 测试成功', 'success');
            } catch (error) {
                addToLog(`handleUpgradeDialog 测试失败: ${error.message}`, 'error');
            }
        }
        
        // 为模拟按钮添加点击事件
        document.getElementById('mockFollowButton').addEventListener('click', function() {
            addToLog('模拟跟进按钮被点击', 'success');
        });
        
        document.getElementById('mockFollowButton2').addEventListener('click', function() {
            addToLog('模拟跟进客户按钮被点击', 'success');
        });
        
        document.getElementById('mockFollowButton3').addEventListener('click', function() {
            addToLog('模拟立即跟进按钮被点击', 'success');
        });
        
        addToLog('测试页面加载完成', 'info');
    </script>
</body>
</html>
