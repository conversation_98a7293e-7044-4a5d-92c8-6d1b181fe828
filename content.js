let isRunning = false;
let settings = null;

// 配置管理系统
const CONFIG = {
  selectors: {
    resetButton: "#app > section > section > main > section > div > form > div:nth-child(1) > div:nth-child(4) > div > div > button:nth-child(2)",
    followButton: 'button',
    textInput: 'textarea.el-textarea__inner',
    saveButton: 'button',
    dialogWrapper: '.el-dialog__wrapper',
    dialogBody: '.el-dialog__body',
    selectDropdown: '.el-select-dropdown',
    closeButton: {
      primary: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__header > button > i",
      secondary: "#app > section > section > main > section > div > div:nth-child(6) > div > div.el-dialog__header > button > i"
    }
  },
  timeouts: {
    elementWait: 5000,
    operationWait: 1000,
    sleepDuration: 30 * 60 * 1000,
    windowCloseDetection: 5000,
    fastDetection: 100
  },
  retryLimits: {
    maxAttempts: 6,
    maxConsecutiveFailures: 3,
    buttonClickRetries: 3
  },
  messages: {
    default: [
      "您好，请问您最近有看车计划吗？",
      "您好，最近有考虑购车吗？",
      "请问您对哪款车型比较感兴趣呢？",
      "您好，需要了解具体车型的信息吗？",
      "最近店内有优惠活动，您有兴趣了解一下吗？"
    ]
  }
};

// 错误类型定义
const ErrorTypes = {
  NETWORK_ERROR: 'network',
  ELEMENT_NOT_FOUND: 'element',
  TIMEOUT_ERROR: 'timeout',
  PERMISSION_ERROR: 'permission',
  VALIDATION_ERROR: 'validation',
  UNKNOWN_ERROR: 'unknown'
};

// 增强的日志系统
class Logger {
  constructor() {
    this.levels = {
      DEBUG: 0,
      INFO: 1,
      WARN: 2,
      ERROR: 3
    };
    this.currentLevel = this.levels.INFO;
    this.sessionId = this.generateSessionId();
  }

  generateSessionId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  log(level, message, context = {}) {
    if (this.levels[level] < this.currentLevel) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      sessionId: this.sessionId,
      url: window.location.href
    };

    this.sendToPopup(logEntry);
    console.log(`[自动跟进助手][${level}] ${message}`, context);
  }

  sendToPopup(logEntry) {
    try {
      chrome.runtime.sendMessage({
        action: 'log',
        data: {
          timestamp: logEntry.timestamp,
          message: logEntry.message,
          type: logEntry.level.toLowerCase()
        }
      });
    } catch (error) {
      console.error('发送日志失败:', error);
    }
  }

  debug(message, context) { this.log('DEBUG', message, context); }
  info(message, context) { this.log('INFO', message, context); }
  warn(message, context) { this.log('WARN', message, context); }
  error(message, context) { this.log('ERROR', message, context); }
}

// 错误处理系统
class ErrorHandler {
  static classifyError(error) {
    if (error.name === 'NetworkError' || error.message.includes('network')) {
      return ErrorTypes.NETWORK_ERROR;
    }
    if (error.message.includes('not found') || error.message.includes('未找到')) {
      return ErrorTypes.ELEMENT_NOT_FOUND;
    }
    if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
      return ErrorTypes.TIMEOUT_ERROR;
    }
    if (error.message.includes('permission')) {
      return ErrorTypes.PERMISSION_ERROR;
    }
    return ErrorTypes.UNKNOWN_ERROR;
  }

  static getRetryStrategy(errorType) {
    const strategies = {
      [ErrorTypes.NETWORK_ERROR]: { shouldRetry: true, delay: 5000, maxRetries: 3 },
      [ErrorTypes.ELEMENT_NOT_FOUND]: { shouldRetry: true, delay: 2000, maxRetries: 5 },
      [ErrorTypes.TIMEOUT_ERROR]: { shouldRetry: true, delay: 3000, maxRetries: 2 },
      [ErrorTypes.PERMISSION_ERROR]: { shouldRetry: false, delay: 0, maxRetries: 0 },
      [ErrorTypes.UNKNOWN_ERROR]: { shouldRetry: true, delay: 1000, maxRetries: 1 }
    };

    return strategies[errorType] || strategies[ErrorTypes.UNKNOWN_ERROR];
  }

  static async handleError(error, context, logger) {
    const errorType = this.classifyError(error);
    const strategy = this.getRetryStrategy(errorType);

    logger.error(`[${context}] ${error.message}`, {
      errorType,
      stack: error.stack,
      strategy
    });

    return strategy;
  }
}

// 元素缓存系统
class ElementCache {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5000; // 5秒缓存
  }

  getElement(selector, validator = null) {
    const cached = this.cache.get(selector);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      // 验证缓存的元素是否仍然有效
      if (document.contains(cached.element)) {
        return cached.element;
      } else {
        // 元素已从DOM中移除，清除缓存
        this.cache.delete(selector);
      }
    }

    const element = document.querySelector(selector);
    if (element && (!validator || validator(element))) {
      this.cache.set(selector, {
        element,
        timestamp: Date.now()
      });
    }

    return element;
  }

  getElements(selector, validator = null) {
    const elements = Array.from(document.querySelectorAll(selector));
    return validator ? elements.filter(validator) : elements;
  }

  clearCache() {
    this.cache.clear();
  }

  clearExpiredCache() {
    const now = Date.now();
    for (const [selector, cached] of this.cache.entries()) {
      if (now - cached.timestamp >= this.cacheTimeout) {
        this.cache.delete(selector);
      }
    }
  }
}

// 内存管理系统
class MemoryManager {
  constructor() {
    this.observers = [];
    this.timers = new Set();
    this.eventListeners = new Map();
    this.intervals = new Set();
  }

  addTimer(timerId) {
    this.timers.add(timerId);
    return timerId;
  }

  addInterval(intervalId) {
    this.intervals.add(intervalId);
    return intervalId;
  }

  addObserver(observer) {
    this.observers.push(observer);
    return observer;
  }

  addEventListener(element, event, handler, options) {
    element.addEventListener(event, handler, options);
    this.eventListeners.set(element, { event, handler });
  }

  cleanup() {
    // 清理定时器
    this.timers.forEach(id => clearTimeout(id));
    this.timers.clear();

    // 清理间隔器
    this.intervals.forEach(id => clearInterval(id));
    this.intervals.clear();

    // 清理观察者
    this.observers.forEach(observer => observer.disconnect());
    this.observers.length = 0;

    // 清理事件监听器
    this.eventListeners.forEach((listener, element) => {
      element.removeEventListener(listener.event, listener.handler);
    });
    this.eventListeners.clear();

    logger.info('内存清理完成');
  }
}

// 状态机系统
class AutomationStateMachine {
  constructor() {
    this.states = {
      IDLE: 'idle',
      FINDING_BUTTON: 'finding_button',
      CLICKING_BUTTON: 'clicking_button',
      FILLING_FORM: 'filling_form',
      SUBMITTING: 'submitting',
      WAITING: 'waiting',
      SLEEPING: 'sleeping',
      ERROR: 'error'
    };

    this.currentState = this.states.IDLE;
    this.previousState = null;
    this.stateHistory = [];
    this.maxHistoryLength = 10;
  }

  async transition(newState, data = {}) {
    if (this.currentState === newState) {
      return; // 避免重复状态转换
    }

    // 安全的日志记录
    try {
      if (typeof logger !== 'undefined' && logger.debug) {
        logger.debug(`状态转换: ${this.currentState} -> ${newState}`, data);
      }
    } catch (error) {
      console.log(`状态转换: ${this.currentState} -> ${newState}`);
    }

    this.previousState = this.currentState;
    this.currentState = newState;

    // 记录状态历史
    this.stateHistory.unshift({
      from: this.previousState,
      to: newState,
      timestamp: Date.now(),
      data
    });

    if (this.stateHistory.length > this.maxHistoryLength) {
      this.stateHistory.pop();
    }

    this.notifyStateChange();
  }

  notifyStateChange() {
    try {
      chrome.runtime.sendMessage({
        action: 'stateChange',
        data: {
          currentState: this.currentState,
          previousState: this.previousState,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      // 安全的错误记录
      try {
        if (typeof logger !== 'undefined' && logger.error) {
          logger.error('状态变更通知失败', { error: error.message });
        }
      } catch (logError) {
        console.error('状态变更通知失败:', error.message);
      }
    }
  }

  getCurrentState() {
    return this.currentState;
  }

  isInState(state) {
    return this.currentState === state;
  }

  getStateHistory() {
    return this.stateHistory;
  }

  reset() {
    this.transition(this.states.IDLE);
  }
}

// 进度指示器
class ProgressIndicator {
  constructor() {
    this.total = 0;
    this.current = 0;
    this.startTime = null;
    this.processingTimes = [];
  }

  start(total) {
    this.total = total;
    this.current = 0;
    this.startTime = Date.now();
    this.processingTimes = [];
    this.updateProgress();
  }

  increment() {
    this.current++;
    const now = Date.now();

    if (this.current > 1) {
      const timePerRecord = (now - this.startTime) / this.current;
      this.processingTimes.push(timePerRecord);

      // 保持最近10次的处理时间
      if (this.processingTimes.length > 10) {
        this.processingTimes.shift();
      }
    }

    this.updateProgress();
  }

  updateProgress() {
    const percentage = this.total > 0 ? Math.round((this.current / this.total) * 100) : 0;
    const eta = this.calculateETA();

    try {
      chrome.runtime.sendMessage({
        action: 'updateProgress',
        data: {
          current: this.current,
          total: this.total,
          percentage,
          eta,
          startTime: this.startTime
        }
      });
    } catch (error) {
      // 安全的错误记录
      try {
        if (typeof logger !== 'undefined' && logger.error) {
          logger.error('进度更新失败', { error: error.message });
        }
      } catch (logError) {
        console.error('进度更新失败:', error.message);
      }
    }
  }

  calculateETA() {
    if (this.processingTimes.length === 0 || this.current === 0) {
      return null;
    }

    const avgTime = this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length;
    const remaining = this.total - this.current;
    return Math.round(remaining * avgTime / 1000); // 返回秒数
  }

  complete() {
    this.current = this.total;
    this.updateProgress();
  }
}

// 调试模式系统
class DebugMode {
  constructor() {
    this.enabled = localStorage.getItem('debugMode') === 'true';
    this.highlightColor = '#ff0000';
    this.highlightDuration = 2000;
  }

  enable() {
    this.enabled = true;
    localStorage.setItem('debugMode', 'true');
    logger.info('调试模式已启用');
  }

  disable() {
    this.enabled = false;
    localStorage.setItem('debugMode', 'false');
    logger.info('调试模式已禁用');
  }

  isEnabled() {
    return this.enabled;
  }

  highlightElement(element, color = this.highlightColor) {
    if (!this.enabled || !element) return;

    const originalOutline = element.style.outline;
    element.style.outline = `2px solid ${color}`;

    setTimeout(() => {
      element.style.outline = originalOutline;
    }, this.highlightDuration);
  }

  logElementInfo(element, action) {
    if (!this.enabled || !element) return;

    const elementInfo = {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      textContent: element.textContent?.substring(0, 50),
      boundingRect: element.getBoundingClientRect(),
      visible: window.getComputedStyle(element).display !== 'none',
      disabled: element.disabled
    };

    logger.debug(`[DEBUG] ${action}`, { element: elementInfo });
  }

  logOperation(operation, data = {}) {
    if (!this.enabled) return;

    logger.debug(`[DEBUG] 操作: ${operation}`, data);
  }

  screenshot() {
    if (!this.enabled) return;

    // 简单的页面状态记录
    const pageState = {
      url: window.location.href,
      title: document.title,
      dialogsCount: document.querySelectorAll('.el-dialog__wrapper').length,
      buttonsCount: document.querySelectorAll('button').length,
      timestamp: new Date().toISOString()
    };

    logger.debug('[DEBUG] 页面状态快照', pageState);
    return pageState;
  }
}

// 性能监控系统
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      operationTimes: [],
      elementSearchTimes: [],
      formFillTimes: []
    };
    this.maxMetrics = 100;
  }

  startTimer(operation) {
    const startTime = performance.now();
    const self = this;

    return {
      operation,
      startTime,
      end() {
        const endTime = performance.now();
        const duration = endTime - startTime;
        self.recordMetric(operation, duration);
        return duration;
      }
    };
  }

  recordMetric(operation, duration) {
    if (!this.metrics[operation]) {
      this.metrics[operation] = [];
    }

    this.metrics[operation].push({
      duration,
      timestamp: Date.now()
    });

    // 保持最近的记录
    if (this.metrics[operation].length > this.maxMetrics) {
      this.metrics[operation].shift();
    }
  }

  getAverageTime(operation) {
    const times = this.metrics[operation] || [];
    if (times.length === 0) return 0;

    const sum = times.reduce((acc, metric) => acc + metric.duration, 0);
    return sum / times.length;
  }

  getMetrics() {
    const summary = {};
    for (const [operation, times] of Object.entries(this.metrics)) {
      summary[operation] = {
        count: times.length,
        average: this.getAverageTime(operation),
        latest: times[times.length - 1]?.duration || 0
      };
    }
    return summary;
  }

  clearMetrics() {
    this.metrics = {
      operationTimes: [],
      elementSearchTimes: [],
      formFillTimes: []
    };
  }
}

// 初始化系统
const logger = new Logger();
const elementCache = new ElementCache();
const memoryManager = new MemoryManager();
const stateMachine = new AutomationStateMachine();
const progressIndicator = new ProgressIndicator();
const debugMode = new DebugMode();
const performanceMonitor = new PerformanceMonitor();

// 页面卸载清理处理
function setupCleanupHandlers() {
  // 页面卸载时清理资源
  window.addEventListener('beforeunload', () => {
    logger.info('页面即将卸载，清理资源');
    memoryManager.cleanup();
    elementCache.clearCache();
    performanceMonitor.clearMetrics();
  });

  // 页面隐藏时暂停操作
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      logger.info('页面已隐藏，暂停操作');
      if (isRunning) {
        // 可以选择暂停或继续运行
        debugMode.logOperation('页面隐藏', { isRunning });
      }
    } else {
      logger.info('页面已显示，恢复操作');
      debugMode.logOperation('页面显示', { isRunning });
    }
  });

  // 错误处理
  window.addEventListener('error', (event) => {
    logger.error('全局错误', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    });
  });

  // 未处理的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    logger.error('未处理的Promise拒绝', {
      reason: event.reason,
      promise: event.promise
    });
  });
}

// 定期清理缓存
function setupPeriodicCleanup() {
  memoryManager.addInterval(setInterval(() => {
    elementCache.clearExpiredCache();

    // 清理旧的性能指标
    const metrics = performanceMonitor.getMetrics();
    if (Object.keys(metrics).length > 0) {
      logger.debug('性能指标', metrics);
    }
  }, 60000)); // 每分钟清理一次
}

// 初始化清理处理器
setupCleanupHandlers();
setupPeriodicCleanup();

// 验证所有系统组件是否正确初始化
function validateInitialization() {
  const components = {
    logger: typeof logger !== 'undefined',
    elementCache: typeof elementCache !== 'undefined',
    memoryManager: typeof memoryManager !== 'undefined',
    stateMachine: typeof stateMachine !== 'undefined',
    progressIndicator: typeof progressIndicator !== 'undefined',
    debugMode: typeof debugMode !== 'undefined',
    performanceMonitor: typeof performanceMonitor !== 'undefined'
  };

  const allInitialized = Object.values(components).every(Boolean);

  if (allInitialized) {
    console.log('[自动跟进助手] 所有组件初始化成功', components);
  } else {
    console.error('[自动跟进助手] 组件初始化失败', components);
  }

  return allInitialized;
}

// 验证初始化
const initSuccess = validateInitialization();

// 发送初始化消息
if (initSuccess) {
  logger.info('Content script已加载，所有组件初始化成功');
} else {
  console.log('[自动跟进助手] Content script已加载，但部分组件初始化失败');
}

chrome.runtime.sendMessage({
  action: 'contentScriptLoaded',
  message: 'Content script已加载',
  initSuccess
});

// 日志和统计管理
const Statistics = {
  totalAttempts: 0,
  successCount: 0,
  failureCount: 0,
  totalSuccess: 0,
  historyTotal: 0,
  buttonFailures: 0,    // 跟进按钮未找到的次数
  isSleeping: false,    // 休眠状态
  sleepEndTime: null,   // 休眠结束时间
  startTime: null,
  logs: [],
  maxLogs: 100000,
  sleepTimer: null,
  skipCurrentRecord: false, // 添加跳过标记
  currentIndex: 0,  // 添加当前位置记录

  start(reset = false) {
    if (reset) {
      this.reset();
    } else {
      this.startTime = new Date();
      this.successCount = 0;
      this.failureCount = 0;
      this.totalAttempts = 0;
      this.buttonFailures = 0;
      this.isSleeping = false;
      this.sleepEndTime = null;
      this.logs = [];
      this.loadHistory();
    }
    this.updatePopup();
    this.addLog('开始自动化流程', 'info');
  },

  loadHistory() {
    const savedTotal = localStorage.getItem('historyTotal');
    const savedSuccess = localStorage.getItem('totalSuccess');
    this.historyTotal = savedTotal ? parseInt(savedTotal) : 0;
    this.totalSuccess = savedSuccess ? parseInt(savedSuccess) : 0;
  },

  saveHistory() {
    localStorage.setItem('historyTotal', this.historyTotal.toString());
    localStorage.setItem('totalSuccess', this.totalSuccess.toString());
  },

  addSuccess() {
    this.successCount++;
    this.totalSuccess++;  // 增加历史成功数
    this.historyTotal++;  // 增加历史总数
    this.saveHistory();   // 保存历史数据
    this.updatePopup();   // 更新显示
    this.addLog('✅ 操作成功', 'success');
  },

  addFailure(isButtonFailure = false) {
    this.failureCount++;
    if (isButtonFailure) {
      this.buttonFailures++;
    }
    this.historyTotal++;  // 增加历史总数
    this.saveHistory();   // 保存历史数据
    this.updatePopup();   // 更新显示
    this.addLog('❌ 操作失败', 'error');
  },

  addLog(message, type = 'info') {
    // 使用新的日志系统
    const logMethod = {
      'info': 'info',
      'success': 'info',
      'error': 'error',
      'warning': 'warn'
    }[type] || 'info';

    // 根据类型添加不同的前缀
    let prefix = '';
    switch(type) {
      case 'success': prefix = '✅ '; break;
      case 'error': prefix = '❌ '; break;
      case 'warning': prefix = '⚠️ '; break;
      case 'info': prefix = 'ℹ️ '; break;
    }

    const logMessage = `${prefix}${message}`;

    // 使用新的日志系统
    logger[logMethod](logMessage, {
      statisticsType: type,
      currentIndex: this.currentIndex,
      isRunning: isRunning
    });

    // 保存到本地日志（保持兼容性）
    const timestamp = new Date().toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    this.logs.unshift({
      time: timestamp,
      message: logMessage,
      type: type
    });

    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    this.updatePopup();
  },

  updatePopup() {
    // 发送完整的统计数据到 popup
    try {
      chrome.runtime.sendMessage({
        action: 'updateStats',
        stats: {
          ...this.getStats(),
          currentSuccess: this.successCount,
          currentFail: this.failureCount,
          totalSuccess: this.totalSuccess,
          historyTotal: this.historyTotal
        }
      });
    } catch (error) {
      console.error('发送统计更新失败:', error);
    }
  },

  getStats() {
    return {
      successCount: this.successCount,
      failureCount: this.failureCount,
      buttonFailures: this.buttonFailures,
      totalSuccess: this.totalSuccess,
      historyTotal: this.historyTotal,
      logs: this.logs,
      status: {
        isRunning,
        isSleeping: this.isSleeping,
        sleepEndTime: this.sleepEndTime
      }
    };
  },

  startSleepTimer() {
    if (this.sleepTimer) {
      clearInterval(this.sleepTimer);
    }

    this.sleepTimer = setInterval(() => {
      // 如果已停止运行，立即清除定时器
      if (!isRunning || !this.isSleeping || !this.sleepEndTime) {
        clearInterval(this.sleepTimer);
        this.sleepTimer = null;
        this.isSleeping = false;
        this.sleepEndTime = null;
        this.updatePopup();
        return;
      }

      // 计算剩余时间（精确到秒）
      const now = new Date();
      const remainingMs = new Date(this.sleepEndTime) - now;
      const remainingMinutes = Math.floor(remainingMs / 60000);
      const remainingSeconds = Math.floor((remainingMs % 60000) / 1000);
      
      // 格式化时间显示 (确保分钟和秒数都是两位数)
      const timeDisplay = `${String(remainingMinutes).padStart(2, '0')}分${String(remainingSeconds).padStart(2, '0')}秒`;

      // 发送实时更新消息
      chrome.runtime.sendMessage({
        action: 'updateSleepStatus',
        data: {
          isSleeping: true,
          timeDisplay,
          remainingMs,
          sleepEndTime: this.sleepEndTime,
          isRunning: isRunning
        }
      });

      if (remainingMs <= 0) {
        this.clearSleepMode();
        this.addLog('休眠结束，继续运行', 'info');
      }

      this.updatePopup();
    }, 1000);
  },

  setSleepMode(minutes) {
    this.isSleeping = true;
    this.sleepEndTime = new Date(Date.now() + minutes * 60 * 1000);
    
    // 立即发送一次状态更新
    const remainingMs = this.sleepEndTime - new Date();
    const remainingMinutes = Math.floor(remainingMs / 60000);
    const remainingSeconds = Math.floor((remainingMs % 60000) / 1000);
    const timeDisplay = `${String(remainingMinutes).padStart(2, '0')}分${String(remainingSeconds).padStart(2, '0')}秒`;

    chrome.runtime.sendMessage({
      action: 'updateSleepStatus',
      data: {
        isSleeping: true,
        timeDisplay,
        remainingMs,
        sleepEndTime: this.sleepEndTime,
        isRunning: isRunning
      }
    });
    
    this.startSleepTimer();
    this.updatePopup();
    this.addLog(`💤 进入休眠模式，${minutes}分钟后自动继续`, 'info');
  },

  clearSleepMode() {
    // 先清除定时器
    if (this.sleepTimer) {
      clearInterval(this.sleepTimer);
      this.sleepTimer = null;
    }

    this.isSleeping = false;
    this.sleepEndTime = null;

    // 发送休眠结束状态
    chrome.runtime.sendMessage({
      action: 'updateSleepStatus',
      data: {
        isSleeping: false,
        timeDisplay: '',
        remainingMs: 0,
        isRunning: isRunning // 使用当前运行状态
      }
    });
    
    this.updatePopup();
  },

  // 修改复位方法
  reset() {
    // 直接复位所有数据
    this.successCount = 0;
    this.failureCount = 0;
    this.totalAttempts = 0;
    this.buttonFailures = 0;
    this.isSleeping = false;
    this.sleepEndTime = null;
    this.logs = [];
    this.totalSuccess = 0;
    this.historyTotal = 0;
    this.currentIndex = 0;

    // 清除本地存储
    localStorage.removeItem('historyTotal');
    localStorage.removeItem('totalSuccess');

    // 更新显示
    this.updatePopup();
    this.addLog('🔄 统计数据已复位', 'info');

    // 发送复位消息到 popup
    try {
      chrome.runtime.sendMessage({
        action: 'statsReset',
        stats: this.getStats()
      });
    } catch (error) {
      console.error('发送复位消息失败:', error);
    }
  },

  moveToNext() {
    this.currentIndex++;
    this.addLog(`📍 移动到第 ${this.currentIndex + 1} 条记录`);
  },
};

// 修改查找跟进按钮的函数
async function findFollowButton() {
  try {
    // 使用缓存获取按钮，验证是否为跟进按钮
    const buttons = elementCache.getElements(CONFIG.selectors.followButton, btn =>
      btn.textContent.includes('跟进') &&
      !btn.disabled &&
      window.getComputedStyle(btn).display !== 'none'
    );

    logger.info(`找到 ${buttons.length} 个跟进按钮，当前索引: ${Statistics.currentIndex}`);
    





    
    // 如果没有找到按钮，尝试重置
    if (!buttons.length) {
      logger.warn('未找到跟进按钮，尝试点击重置按钮');

      const resetButton = elementCache.getElement(CONFIG.selectors.resetButton);
      if (resetButton) {
        await clickButton(resetButton);
        logger.info('点击重置按钮');
        await wait(getOperationWaitTime());
      } else {
        logger.error('未找到重置按钮');
      }

      return null;
    }
    
    // 如果当前索引超出按钮数量，重置为0
    if (Statistics.currentIndex >= buttons.length) {
      Statistics.currentIndex = 0;
      logger.info('索引超出范围，重置为0');
    }

    // 返回当前索引对应的按钮
    const targetButton = buttons[Statistics.currentIndex];
    if (targetButton) {
      logger.info(`选择第 ${Statistics.currentIndex + 1} 个跟进按钮`);
      return targetButton;
    }

    return null;
  } catch (error) {
    await ErrorHandler.handleError(error, 'findFollowButton', logger);
    return null;
  }
}

// 简化的升级通知弹窗处理
async function handleUpgradeDialog() {
  console.log('[自动跟进助手] handleUpgradeDialog 开始');

  try {
    await wait(500); // 简化等待时间
    const dialog = document.querySelector('.el-dialog__wrapper');
    if (!dialog) {
      console.log('[自动跟进助手] 没有找到对话框');
      return;
    }

    const titleEl = dialog.querySelector('.el-dialog__title');
    if (!titleEl || titleEl.textContent !== '升级通知') {
      console.log('[自动跟进助手] 不是升级通知对话框');
      return;
    }

    console.log('[自动跟进助手] 发现升级通知对话框，尝试关闭');
    const buttons = dialog.querySelectorAll('button');
    const closeButton = Array.from(buttons).find(btn =>
      btn.textContent.trim() === '关闭' ||
      btn.textContent.includes('关闭')
    );

    if (closeButton) {
      closeButton.click(); // 直接点击，不使用clickButton避免递归
      await wait(500);
      console.log('[自动跟进助手] 升级通知对话框已关闭');
    }
  } catch (error) {
    console.error('[自动跟进助手] handleUpgradeDialog 错误:', error);
  }

  console.log('[自动跟进助手] handleUpgradeDialog 完成');
}

// 查找文本输入框
// 查找文本输入框的统一函数
async function findTextInput() {
  let attempts = 0;
  const maxAttempts = 6;

  while (attempts < maxAttempts) {
    const textarea = document.querySelector('textarea.el-textarea__inner');
    if (textarea) {
      const style = window.getComputedStyle(textarea);
      if (style.display !== 'none') {
        // 确保输入框在对话框内可见
        const dialogBody = document.querySelector('.el-dialog__body');
        if (dialogBody) {
          const textareaRect = textarea.getBoundingClientRect();
          const dialogRect = dialogBody.getBoundingClientRect();

          // 检查输入框是否在对话框可视区域内
          if (textareaRect.top < dialogRect.top || textareaRect.bottom > dialogRect.bottom) {
            // 滚动到输入框位置
            dialogBody.scrollTo({
              top: dialogBody.scrollTop + (textareaRect.top - dialogRect.top) - (dialogRect.height / 2) + (textareaRect.height / 2),
              behavior: 'smooth'
            });
            await wait(getOperationWaitTime());
          }
        }
        return textarea;
      }
    }
    await wait(getOperationWaitTime());
    attempts++;
  }
  throw new Error('未找到文本输入框');
}

// 修改输入文本函数，添加智能表单填充功能
async function inputText(textInput) {
  try {
    // 1. 直接输入内容
    const dialogBody = document.querySelector('.el-dialog__body');
    if (dialogBody) {
    }

    // 2. 获取随机回复内容并输入文本
    const message = getRandomMessage();
    if (!message) {
      Statistics.addLog('❌ 无法获取有效消息', 'error');
      return false;
    }

    textInput.value = message;
    textInput.dispatchEvent(new InputEvent('input', { bubbles: true }));
    textInput.dispatchEvent(new Event('change', { bubbles: true }));
    await wait(getOperationWaitTime());
    Statistics.addLog('✍️ 文本输入完成');

    // 3. 智能表单自动填充（如果启用）
    if (settings && settings.autoFillForm) {
      await autoFillForm();
    } else {
      Statistics.addLog('⚠️ 智能表单填充已禁用，跳过');
    }

    // 4. 第一次点击保存按钮

    // 移除保存前等待，直接执行

    const saveButton = Array.from(document.querySelectorAll('button'))
      .find(btn => btn.textContent.includes('保存'));

    if (!saveButton) {
      Statistics.addLog('❌ 未找到保存按钮', 'error');
      return false;
    }

    Statistics.addLog('🖱️ 点击保存按钮');
    saveButton.click();
    await wait(1); // 最小必要等待

    // 4. 检测窗口是否自动关闭
    const initialDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
    const initialCount = initialDialogs.length;

    // 使用智能检测窗口关闭
    const windowClosed = await smartDetectWindowClosed(initialCount, 100);

    if (windowClosed) {
      Statistics.addLog('✅ 检测到窗口已自动关闭');
      return true;
    }

    // 5. 如果窗口未自动关闭，直接再次点击保存
    Statistics.addLog('⚠️ 窗口未自动关闭，直接再次点击保存');

    // 6. 再次点击保存按钮
    Statistics.addLog('🖱️ 再次点击保存按钮');
    saveButton.click();
    await wait(getOperationWaitTime());

    // 7. 再次检测窗口是否自动关闭
    const secondStartTime = Date.now();
    while (Date.now() - secondStartTime < maxWaitTime) {
      const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      if (currentDialogs.length < initialCount) {
        Statistics.addLog('✅ 检测到窗口已自动关闭');
        return true;
      }
      await wait(1); // 最小检测间隔
    }

    // 8. 如果还是未自动关闭，尝试手动关闭
    Statistics.addLog('⚠️ 窗口仍未自动关闭，尝试手动关闭', 'warning');

    const closeButton = document.querySelector("#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__header > button > i") ||
                       document.querySelector("#app > section > section > main > section > div > div:nth-child(6) > div > div.el-dialog__header > button > i");

    if (closeButton) {
      Statistics.addLog('🖱️ 点击关闭按钮');
      closeButton.click();
      await wait(getOperationWaitTime());

      const finalDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      if (finalDialogs.length < initialCount) {
        Statistics.addLog('✅ 手动关闭窗口成功，将跳过此条记录');
        Statistics.currentIndex++;
        Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
        await wait(getOperationWaitTime() * 2);
        return true;
      }
    }

    Statistics.addLog('❌ 窗口关闭失败，将跳过当前记录', 'warning');
    Statistics.currentIndex++;
    Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
    await wait(getOperationWaitTime() * 2);
    return true;

  } catch (error) {
    Statistics.addLog(`❌ 操作失败: ${error.message}`, 'error');
    throw error;
  }
}

async function performSingleFollow() {
  try {
    // 首先检查是否需要跳过当前记录
    if (Statistics.skipCurrentRecord) {
      Statistics.skipCurrentRecord = false;
      Statistics.moveToNext(); // 移动到下一条记录
      Statistics.addLog('⏭️ 跳过当前记录，继续处理下一条');
      await wait(1); // 移除不必要的跳过等待
      return true;
    }

    // 查找跟进按钮
    const followButton = await findFollowButton();
    if (!followButton) {
      Statistics.addFailure(true);
      Statistics.addLog('❌ 未找到跟进按钮，等待10秒后重试', 'error');
      // 如果当前索引不为0，重置索引
      if (Statistics.currentIndex > 0) {
        Statistics.currentIndex = 0;
        Statistics.addLog('🔄 重置到第一条记录');
      }
      await wait(10000);
      return false;
    }
    
    Statistics.addLog('找到跟进按钮，准备点击');
    console.log('[自动跟进助手] 即将调用 clickButton');

    try {
      await clickButton(followButton);
      console.log('[自动跟进助手] clickButton 调用完成');
      Statistics.addLog('已点击跟进按钮');
    } catch (error) {
      console.error('[自动跟进助手] clickButton 调用失败:', error);
      throw error;
    }

    console.log('[自动跟进助手] 即将调用 handleUpgradeDialog');
    await handleUpgradeDialog();
    console.log('[自动跟进助手] handleUpgradeDialog 调用完成');

    const textInput = await findTextInput();
    Statistics.addLog('找到文本输入框');

    // 继续正常的处理流程
    const result = await inputText(textInput);

    if (result) {
      // 只有在没有设置skipCurrentRecord时才计算成功
      if (!Statistics.skipCurrentRecord) {
        Statistics.addSuccess();
        Statistics.addLog('本次跟进操作完成', 'success');
      }

      const waitTime = getRandomWaitTime();
      const waitTimeDisplay = waitTime < 1 ?
        `${(waitTime * 1000).toFixed(0)}毫秒` :
        `${waitTime}秒`;
      Statistics.addLog(`⏳ 等待${waitTimeDisplay}后开始下一轮...`);
      await wait(waitTime);
      return true;
    } else {
      Statistics.addFailure();
      Statistics.addLog('❌ 跟进操作失败', 'error');
      await wait(10000);
      return false;
    }
  } catch (error) {
    Statistics.addFailure();
    Statistics.addLog(`❌ ${error.message}`, 'error');
    await wait(10000);
    return false;
  }
}



// 添加检查元素是否在容器可视区域内的辅助函数
function isElementVisible(element, container) {
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  
  return (
    elementRect.top >= containerRect.top &&
    elementRect.bottom <= containerRect.bottom
  );
}



// 添加查找下一条按钮的函数
async function findNextButton() {
  try {
    // 等待下一条按钮出现
    const nextButton = await waitForElement('button.el-button--default:not(.el-button--primary):not(.is-disabled)');
    if (nextButton && nextButton.textContent.includes('下一条')) {
      return nextButton;
    }
    return null;
  } catch (error) {
    Statistics.addLog('⚠️ 未找到下一条按钮', 'warning');
    return null;
  }
}

// 添加等待元素出现的辅助函数
async function waitForElement(selector, timeout = 5000) {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    const element = document.querySelector(selector);
    if (element) {
      return element;
    }
    await wait(100);
  }
  return null;
}

// 优化的自动化流程函数，集成状态机
async function startAutomation() {
  try {
    // 安全的状态转换
    try {
      await stateMachine.transition(stateMachine.states.FINDING_BUTTON);
    } catch (stateError) {
      console.warn('状态转换失败，继续执行:', stateError.message);
    }

    Statistics.start();
    logger.info('开始自动化流程');

    // 初始化进度指示器 - 延迟获取按钮数量
    try {
      const buttons = elementCache.getElements(CONFIG.selectors.followButton, btn =>
        btn.textContent && btn.textContent.includes('跟进') && !btn.disabled
      );
      progressIndicator.start(buttons.length || 0);
      logger.info(`找到 ${buttons.length} 个跟进按钮`);
    } catch (error) {
      logger.warn('获取按钮数量失败，使用默认值', { error: error.message });
      progressIndicator.start(0);
    }

    let consecutiveFailures = 0;
    const maxConsecutiveFailures = CONFIG.retryLimits.maxConsecutiveFailures;
    const sleepTime = CONFIG.timeouts.sleepDuration / (60 * 1000); // 转换为分钟

    while (isRunning) {
      try {
        Statistics.totalAttempts++;
        const success = await performSingleFollow();

        if (success) {
          consecutiveFailures = 0;
          progressIndicator.increment();
          try {
            await stateMachine.transition(stateMachine.states.WAITING);
          } catch (stateError) {
            console.warn('状态转换失败:', stateError.message);
          }
        } else {
          consecutiveFailures++;
          try {
            await stateMachine.transition(stateMachine.states.ERROR, {
              consecutiveFailures,
              maxConsecutiveFailures
            });
          } catch (stateError) {
            console.warn('状态转换失败:', stateError.message);
          }

          if (consecutiveFailures >= maxConsecutiveFailures) {
            await enterSleepMode(sleepTime);
            consecutiveFailures = 0;
          }
        }
      } catch (error) {
        consecutiveFailures++;
        await ErrorHandler.handleError(error, 'startAutomation', logger);
        await stateMachine.transition(stateMachine.states.ERROR, {
          error: error.message,
          consecutiveFailures
        });

        if (consecutiveFailures >= maxConsecutiveFailures) {
          await enterSleepMode(sleepTime);
          consecutiveFailures = 0;
        }
      }

      // 等待一段时间再进行下一次尝试
      if (isRunning) {
        await wait(getRandomWaitTime() * 1000);
      }
    }
  } catch (error) {
    logger.error('自动化流程出现严重错误', { error: error.message });
    await stateMachine.transition(stateMachine.states.ERROR, { error: error.message });
  } finally {
    // 确保在退出时清理所有状态
    if (Statistics.isSleeping) {
      Statistics.clearSleepMode();
    }
    await stateMachine.transition(stateMachine.states.IDLE);
    progressIndicator.complete();
    logger.info('自动化流程已停止');
  }
}

// 休眠模式处理函数
async function enterSleepMode(sleepTimeMinutes) {
  await stateMachine.transition(stateMachine.states.SLEEPING, {
    sleepTime: sleepTimeMinutes
  });

  Statistics.setSleepMode(sleepTimeMinutes);
  logger.warn(`进入休眠模式 ${sleepTimeMinutes} 分钟`);

  const sleepStartTime = Date.now();
  const sleepDuration = sleepTimeMinutes * 60000;

  while (isRunning && Statistics.isSleeping && Date.now() - sleepStartTime < sleepDuration) {
    await wait(1000); // 每秒检查一次
  }

  if (!isRunning) {
    Statistics.clearSleepMode();
    return;
  }

  if (isRunning) {
    Statistics.clearSleepMode();
    logger.info('休眠结束，重新开始运行');
    await stateMachine.transition(stateMachine.states.FINDING_BUTTON);
  }
}

// 优化的消息获取函数
function getRandomMessage() {
  try {
    // 检查 settings 是否存在
    if (!settings || !settings.messages || !Array.isArray(settings.messages) || settings.messages.length === 0) {
      logger.info('使用默认消息列表');
      return CONFIG.messages.default[Math.floor(Math.random() * CONFIG.messages.default.length)];
    }

    // 过滤有效消息
    const validMessages = settings.messages.filter(msg => msg && typeof msg === 'string' && msg.trim());

    if (validMessages.length === 0) {
      logger.warn('没有有效的自定义消息，使用默认消息');
      return CONFIG.messages.default[Math.floor(Math.random() * CONFIG.messages.default.length)];
    }

    const selectedMessage = validMessages[Math.floor(Math.random() * validMessages.length)];
    logger.debug('选择消息', { message: selectedMessage });
    return selectedMessage;
  } catch (error) {
    logger.error('获取消息出错，使用默认消息', { error: error.message });
    return CONFIG.messages.default[Math.floor(Math.random() * CONFIG.messages.default.length)];
  }
}

// 修改获取随机等待时间的函数（用于跟进操作之间的等待）
function getRandomWaitTime() {
  // 将输入值转换为浮点数，支持小数
  const minTime = parseFloat(settings?.minWaitTime) || 0.1;
  const maxTime = parseFloat(settings?.maxWaitTime) || 0.2;

  // 确保最小值不小于0.1秒
  const safeMinTime = Math.max(0.1, minTime);
  const safeMaxTime = Math.max(safeMinTime, maxTime);

  // 生成随机等待时间（支持小数）
  const randomTime = Math.round((Math.random() * (safeMaxTime - safeMinTime) + safeMinTime) * 10) / 10;

  return randomTime;
}

// 新增：获取操作等待时间的函数（用于页面操作之间的等待）
function getOperationWaitTime() {
  // 将输入值转换为浮点数，支持小数
  const minTime = parseFloat(settings?.minWaitTime) || 0.1;
  const maxTime = parseFloat(settings?.maxWaitTime) || 0.2;

  // 确保最小值不小于0.05秒，最大值不超过0.5秒（页面操作应该更快）
  const safeMinTime = Math.max(0.05, Math.min(minTime, 0.3));
  const safeMaxTime = Math.max(safeMinTime, Math.min(maxTime, 0.5));

  // 生成随机等待时间（支持小数）
  const randomTime = Math.random() * (safeMaxTime - safeMinTime) + safeMinTime;
  return Math.round(randomTime * 100) / 100; // 保留2位小数
}

// 修改等待函数，支持小数秒
function wait(ms) {
  // 如果输入是秒，转换为毫秒
  if (ms < 100) { // 假设小于100的是秒单位
    ms = ms * 1000;
  }
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 极速智能检测：等待条件满足
async function fastWaitForCondition(conditionFn, maxAttempts = 50) {
  for (let i = 0; i < maxAttempts; i++) {
    if (conditionFn()) {
      return true;
    }
    await wait(1); // 1ms极速检测
  }
  return false;
}

// 极速智能检测：等待元素可见
async function fastWaitForVisible(selector, maxAttempts = 50) {
  return fastWaitForCondition(() => {
    const element = document.querySelector(selector);
    return element && element.offsetParent !== null;
  }, maxAttempts);
}

// 智能检测窗口是否关闭
async function smartDetectWindowClosed(initialCount, maxAttempts = 100) {
  return fastWaitForCondition(() => {
    // 方法1: 检查对话框数量
    const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
    const visibleDialogs = Array.from(currentDialogs).filter(dialog => {
      const style = window.getComputedStyle(dialog);
      return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    });

    // 方法2: 检查对话框内容是否存在
    const dialogBodies = document.querySelectorAll('.el-dialog__body');
    const visibleBodies = Array.from(dialogBodies).filter(body => {
      const style = window.getComputedStyle(body);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });

    // 方法3: 检查遮罩层
    const masks = document.querySelectorAll('.el-dialog__wrapper .v-modal');
    const visibleMasks = Array.from(masks).filter(mask => {
      const style = window.getComputedStyle(mask);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });

    return visibleDialogs.length < initialCount ||
           visibleBodies.length === 0 ||
           visibleMasks.length === 0;
  }, maxAttempts);
}

// 优化的消息监听器
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  try {
    logger.debug('收到消息', { action: request.action });

    if (request.action === 'start') {
      if (!isRunning) {
        isRunning = true;
        settings = request.settings;

        // 记录设置信息
        if (settings.minWaitTime && settings.maxWaitTime) {
          logger.info(`等待时间设置: ${settings.minWaitTime}-${settings.maxWaitTime}秒`);
        }

        // 启用调试模式（如果设置中有）
        if (settings.debugMode) {
          debugMode.enable();
        }

        // 立即更新状态
        Statistics.updatePopup();

        // 发送立即状态更新
        chrome.runtime.sendMessage({
          action: 'updateSleepStatus',
          data: {
            isSleeping: Statistics.isSleeping,
            remainingTime: 0,
            sleepEndTime: null,
            isRunning: true
          }
        });

        startAutomation().catch(error => {
          logger.error('自动化流程错误', { error: error.message, stack: error.stack });
          isRunning = false;
          Statistics.updatePopup();
        });

        sendResponse({
          status: 'started',
          currentState: {
            isRunning: true,
            isSleeping: Statistics.isSleeping,
            stats: Statistics.getStats(),
            debugMode: debugMode.isEnabled()
          }
        });
      } else {
        logger.warn('尝试启动已运行的自动化流程');
        sendResponse({ status: 'error', error: '已经在运行中' });
      }
      return true;
    }

    if (request.action === 'stop') {
      isRunning = false;

      // 立即清除休眠状态
      if (Statistics.isSleeping) {
        Statistics.clearSleepMode();
      }

      // 状态机转换到停止状态
      stateMachine.transition(stateMachine.states.IDLE);

      // 立即发送停止状态
      chrome.runtime.sendMessage({
        action: 'updateSleepStatus',
        data: {
          isSleeping: false,
          timeDisplay: '',
          remainingMs: 0,
          isRunning: false
        }
      });

      logger.info('停止自动化流程');
      Statistics.updatePopup();

      sendResponse({ status: 'stopped' });
      return true;
    }

    if (request.action === 'getStats') {
      sendResponse(Statistics.getStats());
      return true;
    }

    if (request.action === 'getState') {
      sendResponse({
        isRunning,
        stats: Statistics.getStats(),
        currentState: stateMachine.getCurrentState(),
        debugMode: debugMode.isEnabled()
      });
      return true;
    }

    if (request.action === 'resetStats') {
      Statistics.reset();
      performanceMonitor.clearMetrics();
      logger.info('统计数据已重置');
      sendResponse({ success: true });
      return true;
    }

    if (request.action === 'toggleDebug') {
      if (debugMode.isEnabled()) {
        debugMode.disable();
      } else {
        debugMode.enable();
      }
      sendResponse({ debugMode: debugMode.isEnabled() });
      return true;
    }

    if (request.action === 'getPerformanceMetrics') {
      sendResponse(performanceMonitor.getMetrics());
      return true;
    }

  } catch (error) {
    logger.error('消息处理错误', {
      action: request.action,
      error: error.message
    });
    sendResponse({ status: 'error', error: error.message });
  }
});

// 发送就绪消息
console.log('[自动跟进助手] Content script准备就绪');
chrome.runtime.sendMessage({
  action: 'contentScriptReady',
  url: window.location.href
});

// 辅助函数：获取元素的路径
function getElementPath(element) {
  const path = [];
  while (element && element.nodeType === Node.ELEMENT_NODE) {
    let selector = element.nodeName.toLowerCase();
    if (element.id) {
      selector += `#${element.id}`;
    } else if (element.className) {
      selector += `.${element.className.replace(/\s+/g, '.')}`;
    }
    path.unshift(selector);
    element = element.parentNode;
  }
  return path.join(' > ');
}

// 添加事件监听器的辅助函数
function addPassiveEventListener(element, eventType, handler) {
  element.addEventListener(eventType, handler, { passive: true });
}

// 极简的点击按钮函数
async function clickButton(button) {
  console.log('[自动跟进助手] clickButton 开始执行');

  if (!button) {
    console.error('[自动跟进助手] 按钮为空');
    throw new Error('按钮为空');
  }

  console.log('[自动跟进助手] 按钮文本:', button.textContent?.substring(0, 20));

  try {
    // 直接点击，不做复杂验证
    button.click();
    console.log('[自动跟进助手] 按钮点击完成');
    return true;
  } catch (error) {
    console.error('[自动跟进助手] 按钮点击失败:', error.message);
    throw error;
  }
}



// 处理选择框的通用函数
async function handleSelect(type, selector) {
  try {
    Statistics.addLog(`检查${type}选择框`);
    
    // 使用提供的选择器查找选择框
    const select = document.querySelector(selector);
    // 修改备用选择器，确保能找到正确的元素
    const altSelect = document.querySelector(`.el-dialog__body .el-select input[placeholder="请选择"]`);
    
    // 合并选择器检查
    const targetSelect = select || altSelect;
    if (!targetSelect) {
      Statistics.addLog(`未找到${type}选择框，可能不需要选择`, 'info');
      return true;
    }

    // 检查是否为"请选择"状态
    const input = targetSelect.querySelector('input[placeholder="请选择"]');
    if (!input || input.value !== '') {
      Statistics.addLog(`${type}已选择，无需操作`, 'info');
      return true;
    }

    // 点击选择框并等待下拉框
    targetSelect.click();
    // 智能检测下拉框是否展开
    await fastWaitForVisible('.el-select-dropdown:not([style*="display: none"])');
    
    // 查找可见的下拉框
    const visibleDropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"])');
    if (!visibleDropdown) {
      Statistics.addLog('未找到可见的下拉框', 'warning');
      return false;
    }

    // 在可见下拉框中查找第一个可用选项
    const firstOption = visibleDropdown.querySelector('.el-select-dropdown__item:not(.is-disabled)');
    if (!firstOption) {
      Statistics.addLog(`未找到${type}可用选项`, 'warning');
      return false;
    }

    // 点击第一个选项
    firstOption.click();
    await wait(1); // 最小必要等待
    
    // 验证选择是否成功
    if (targetSelect.value === '请选择') {
      Statistics.addLog(`${type}选择可能未成功，重试一次`, 'warning');
      firstOption.click();
      await wait(500);
    }

    Statistics.addLog(`已选择${type}选项`);
    return true;

  } catch (error) {
    Statistics.addLog(`${type}选择失败: ${error.message}`, 'error');
    return false;
  }
}

// 必填选择框配置（按网页顺序）
const REQUIRED_FIELD_CONFIG = {
  // 按照网页上的顺序排列
  '线索是否有效': {
    options: ['有效线索', '待定'],
    defaultValue: '有效线索',
    required: true
  },
  '意向车系': {
    options: ['Q5L', 'A4L Limousine', 'A6L Limousine', 'Q3', 'A3 Limousine'],
    defaultValue: 'Q5L',
    required: true
  },
  '预购日期': {
    type: 'date',
    required: true
  },
  '线索等级': {
    options: ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）'],
    defaultValue: 'B（30天内跟进）',
    required: true
  },
  '跟进状态': {
    options: ['再次待跟进', '有意向到店', '已到店交接', '申请无效'],
    defaultValue: '再次待跟进',
    required: true
  },
  '跟进方式': {
    options: ['电话沟通', '微信交流', '面对面沟通'],
    defaultValue: '电话沟通',
    required: true
  },
  '计划跟进时间': {
    type: 'date',
    required: true
  }
};

// 智能表单自动填充函数
async function autoFillForm() {
  try {
    Statistics.addLog('🤖 开始智能表单填充');
    let filledCount = 0;

    // 等待表单加载
    await wait(getOperationWaitTime());

    // 只处理必填字段（带*号），完全跳过非必填选择框
    Statistics.addLog('📋 跳过所有非必填选择框，只处理带*号的必填字段');

    if (settings && settings.forceRequired) {
      Statistics.addLog('🔥 启用强制填充必填字段（强制模式：处理所有必填字段）');
      await forceHandleRequiredFields();
    } else {
      Statistics.addLog('🔍 智能检测模式：只处理空的必填字段');
      await forceHandleRequiredFields(); // 使用智能检测
    }

    filledCount = 1; // 文本输入算作1个字段

    // 注意：必填的日期字段（预购日期、计划跟进时间）已在上面处理
    // 这里只处理其他非必填的日期字段（如果需要的话）
    Statistics.addLog('📅 必填日期字段已处理，跳过其他日期字段');

    // 处理文本区域
    await fillTextAreas();

    Statistics.addLog(`✅ 表单填充完成，共填充 ${filledCount} 个字段`, 'success');
    return true;

  } catch (error) {
    Statistics.addLog(`❌ 表单填充失败: ${error.message}`, 'error');
    return false;
  }
}



// 强制处理必填字段（带*号）- 使用精确路径
async function forceHandleRequiredFields() {
  try {
    // 使用全局设置变量
    if (settings && settings.forceRequired) {
      Statistics.addLog('🔥 开始强制处理必填字段（强制模式：处理所有字段）');
    } else {
      Statistics.addLog('🔍 开始智能检测必填字段（只处理空字段）');
    }

    // 使用精确的CSS选择器路径处理关键必填字段
    await handleRequiredFieldByPath();

  } catch (error) {
    Statistics.addLog(`处理必填字段失败: ${error.message}`, 'error');
  }
}

// 智能检测空的必填字段
async function detectEmptyRequiredFields(requiredFields) {
  const fieldsToProcess = [];

  // 使用全局设置变量
  const forceMode = settings && settings.forceRequired;

  for (const field of requiredFields) {
    try {
      const element = document.querySelector(field.path);
      if (!element) {
        Statistics.addLog(`⚠️ 未找到${field.name}元素，将尝试处理`);
        fieldsToProcess.push(field);
        continue;
      }

      let isEmpty = false;

      if (field.type === 'select') {
        // 检查选择框是否有值
        const selectInput = element.querySelector('input, .el-input__inner');
        if (selectInput) {
          const value = selectInput.value || selectInput.placeholder;
          isEmpty = !value || value.trim() === '' || value.includes('请选择') || value.includes('全部');

          // 特殊处理线索等级：如果不是30天就强制修改
          if (field.name === '线索等级') {
            const isNot30Days = !value.includes('30天内跟进');
            if (isEmpty || isNot30Days) {
              fieldsToProcess.push(field);
              if (isNot30Days && !isEmpty) {
                Statistics.addLog(`🔄 线索等级当前为"${value}"，将修改为30天内跟进`);
              }
            }
          } else {
            if (forceMode) {
              fieldsToProcess.push(field);
            } else {
              if (isEmpty) {
                fieldsToProcess.push(field);
              }
            }
          }
        } else {
          isEmpty = true;
          Statistics.addLog(`🔍 ${field.name}未找到输入框，视为空`);
          fieldsToProcess.push(field);
        }
      } else if (field.type === 'date') {
        // 检查日期字段是否有值
        const dateInput = element.querySelector('input');
        if (dateInput) {
          const value = dateInput.value;
          isEmpty = !value || value.trim() === '';

          if (forceMode) {
            fieldsToProcess.push(field);
          } else {
            if (isEmpty) {
              fieldsToProcess.push(field);
            }
          }
        } else {
          isEmpty = true;
          Statistics.addLog(`🔍 ${field.name}未找到输入框，视为空`);
          fieldsToProcess.push(field);
        }
      }

    } catch (error) {
      Statistics.addLog(`⚠️ 检测${field.name}时出错: ${error.message}，将尝试处理`);
      fieldsToProcess.push(field);
    }
  }

  return fieldsToProcess;
}

// 使用精确路径处理必填字段 - 智能检测版本
async function handleRequiredFieldByPath() {
  try {
    // 定义所有必填字段的配置（按网页顺序）
    const requiredFields = [
      {
        name: '线索是否有效',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div > div > div",
        type: 'select',
        options: ['有效线索', '待定']
      },
      {
        name: '意向车系',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(2) > div:nth-child(2) > div > div > div",
        type: 'select',
        options: ['Q5L', 'A4L Limousine', 'A6L Limousine', 'Q3', 'A3 Limousine']
      },
      {
        name: '预购日期',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(3) > div:nth-child(1) > div > div > div.el-date-editor.el-input.el-input--small.el-input--prefix.el-input--suffix.el-date-editor--datetime",
        type: 'date'
      },
      {
        name: '线索等级',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(7) > div:nth-child(1) > div > div > div",
        type: 'select',
        options: ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）']
      },
      {
        name: '跟进状态',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(7) > div:nth-child(2) > div > div > div",
        type: 'select',
        options: ['再次待跟进', '有意向到店', '已到店交接', '申请无效']
      },
      {
        name: '跟进方式',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(7) > div:nth-child(3) > div > div > div",
        type: 'select',
        options: ['电话沟通', '微信交流', '面对面沟通']
      },
      {
        name: '计划跟进时间',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(8) > div:nth-child(2) > div > div > div.el-date-editor.el-input.el-input--small.el-input--prefix.el-input--suffix.el-date-editor--datetime",
        type: 'date'
      }
    ];

    // 智能检测需要处理的字段
    const fieldsToProcess = await detectEmptyRequiredFields(requiredFields);

    if (fieldsToProcess.length === 0) {
      Statistics.addLog('✅ 所有必填字段都已有值，无需处理');
      return;
    }

    Statistics.addLog(`🔍 检测到 ${fieldsToProcess.length} 个必填字段需要处理: ${fieldsToProcess.map(f => f.name).join(', ')}`);

    // 逐个处理需要填充的字段
    for (const field of fieldsToProcess) {
      try {
    

        let success = false;
        if (field.type === 'select') {
          success = await handleSelectByPath(field.path, field.name, field.options);
        } else if (field.type === 'date') {
          success = await handleElementUIDatePicker(field.path, field.name);
        }

        if (success) {
          Statistics.addLog(`✅ ${field.name}处理成功`);
        }

        await wait(getOperationWaitTime());

      } catch (error) {
        Statistics.addLog(`❌ 处理${field.name}时出错: ${error.message}`, 'error');
      }
    }



  } catch (error) {
    Statistics.addLog(`精确路径处理失败: ${error.message}`, 'error');
  }
}

// 使用精确路径处理选择框
async function handleSelectByPath(cssPath, fieldName, expectedOptions) {
  try {
    const element = document.querySelector(cssPath);
    if (!element) {
      return false;
    }

    // 滚动到元素位置
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(500);

    // 查找选择框
    const selectBox = element.querySelector('.el-select') || element.closest('.el-select') || element;
    if (!selectBox) {
      Statistics.addLog(`❌ ${fieldName}中未找到选择框`);
      return false;
    }

    // 强制点击选择框

    selectBox.click();
    // 智能检测选项是否加载完成
    await fastWaitForCondition(() => {
      const dropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"])');
      return dropdown && dropdown.querySelectorAll('.el-select-dropdown__item').length > 0;
    });

    // 查找下拉选项
    let dropdown = null;
    for (let i = 0; i < 5; i++) {
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
      if (dropdowns.length > 0) {
        dropdown = dropdowns[dropdowns.length - 1];
        break;
      }
      await wait(300);
    }

    if (!dropdown) {
      Statistics.addLog(`❌ ${fieldName}下拉框未出现`);
      return false;
    }

    const options = dropdown.querySelectorAll('.el-select-dropdown__item');


    if (options.length > 0) {
      // 查找匹配的选项
      let selectedOption = null;

      for (const expectedOption of expectedOptions) {
        selectedOption = Array.from(options).find(opt =>
          opt.textContent.trim() === expectedOption
        );
        if (selectedOption) break;
      }

      // 如果没找到预期选项，选择第一个非"全部"的选项
      if (!selectedOption) {
        selectedOption = Array.from(options).find(opt => {
          const text = opt.textContent.trim();
          return text !== '' && text !== '全部' && text !== '请选择';
        });
      }

      if (selectedOption) {
        selectedOption.click();
        await wait(getOperationWaitTime());

        // 关闭下拉框
        document.body.click();
        await wait(getOperationWaitTime());

        Statistics.addLog(`✅ ${fieldName}设置成功`);
        return true;
      }
    }

    Statistics.addLog(`❌ ${fieldName}未找到合适选项`);
    document.body.click(); // 关闭下拉框
    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 使用精确路径处理日期字段 - 模拟真实用户交互
async function handleDateByPath(cssPath, fieldName) {
  try {
    Statistics.addLog(`🔍 查找${fieldName}日期编辑器: ${cssPath}`);

    // 直接定位到el-date-editor元素
    const dateEditor = document.querySelector(cssPath);
    if (!dateEditor) {
      Statistics.addLog(`❌ 未找到${fieldName}日期编辑器`);
      return false;
    }

    Statistics.addLog(`✅ 找到${fieldName}日期编辑器`);

    // 滚动到元素位置
    dateEditor.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(500);

    // 查找日期编辑器内的输入框
    const dateInput = dateEditor.querySelector('input') || dateEditor.querySelector('.el-input__inner');
    if (!dateInput) {
      Statistics.addLog(`❌ ${fieldName}日期编辑器中未找到输入框`);
      return false;
    }

    Statistics.addLog(`✅ 找到${fieldName}输入框`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(10 + Math.floor(Math.random() * 8));
    futureDate.setMinutes(Math.floor(Math.random() * 60));

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 准备通过模拟交互设置${fieldName}: ${dateString}`);

    // 模拟真实用户交互：点击输入框打开日期选择器
    Statistics.addLog(`📅 点击${fieldName}输入框打开日期选择器`);

    // 多次尝试点击，确保日期选择器打开
    for (let clickAttempt = 1; clickAttempt <= 3; clickAttempt++) {
      dateInput.click();
      // 智能检测日期选择器是否打开
      const opened = await fastWaitForVisible('.el-picker-panel:not([style*="display: none"])');
      if (opened) break;

      // 检查日期选择器是否打开
      const panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
      Statistics.addLog(`📅 第${clickAttempt}次点击后找到 ${panels.length} 个日期选择器面板`);

      if (panels.length > 0) {
        Statistics.addLog(`✅ 日期选择器已打开`);
        break;
      }

      if (clickAttempt < 3) {
        Statistics.addLog(`⚠️ 日期选择器未打开，重试点击`);
      }
    }

    // 查找并点击"此刻"按钮
    const nowButtonSelector = "body > div.el-picker-panel.el-date-picker.el-popper.has-time > div.el-picker-panel__footer > button.el-button.el-picker-panel__link-btn.el-button--text.el-button--mini";

    for (let attempt = 1; attempt <= 5; attempt++) {
      try {
        Statistics.addLog(`📅 第${attempt}次尝试查找"此刻"按钮`);

        // 智能检测"此刻"按钮是否可用
        await fastWaitForVisible('.el-picker-panel__footer button');

        // 调试：查找所有可能的日期选择器面板
        const allPanels = document.querySelectorAll('.el-picker-panel');
        Statistics.addLog(`🔍 找到 ${allPanels.length} 个日期选择器面板`);

        // 调试：查找所有可能的按钮
        const allButtons = document.querySelectorAll('.el-picker-panel__footer button');
        Statistics.addLog(`🔍 找到 ${allButtons.length} 个日期选择器按钮`);

        if (allButtons.length > 0) {
          const buttonTexts = Array.from(allButtons).map(btn => btn.textContent.trim());
          Statistics.addLog(`🔍 按钮文本: ${buttonTexts.join(', ')}`);
        }

        const nowButton = document.querySelector(nowButtonSelector);
        if (nowButton && nowButton.offsetParent !== null) { // 确保按钮可见
          Statistics.addLog(`✅ 找到"此刻"按钮，准备点击`);
          nowButton.click();
          // 智能检测日期是否已设置
          await fastWaitForCondition(() => {
            const input = document.querySelector(`${cssPath} input`);
            return input && input.value && input.value.trim() !== '';
          });

          // 查找输入框验证是否设置成功
          const dateInput = document.querySelector(`${cssPath} input`) ||
                           document.querySelector(`${cssPath} .el-input__inner`) ||
                           dateEditor.querySelector('input') ||
                           dateEditor.querySelector('.el-input__inner');

          if (dateInput && dateInput.value && dateInput.value.trim() !== '') {
            Statistics.addLog(`✅ ${fieldName}通过"此刻"按钮设置成功: ${dateInput.value}`);

            // 点击页面其他地方关闭日期选择器
            document.body.click();
            await wait(300);

            return true;
          } else {
            Statistics.addLog(`⚠️ ${fieldName}点击"此刻"按钮后未检测到值变化`);
          }
        } else {
          Statistics.addLog(`❌ 第${attempt}次未找到"此刻"按钮或按钮不可见`);

          // 尝试查找其他可能的"此刻"按钮选择器
          const alternativeSelectors = [
            "button:contains('此刻')",
            ".el-picker-panel__footer button",
            ".el-button--text:contains('此刻')",
            ".el-picker-panel__link-btn"
          ];

          for (const selector of alternativeSelectors) {
            try {
              const buttons = document.querySelectorAll(selector);
              for (const btn of buttons) {
                if (btn.textContent.includes('此刻') || btn.textContent.includes('现在')) {
                  Statistics.addLog(`✅ 通过备用选择器找到"此刻"按钮: ${btn.textContent.trim()}`);
                  btn.click();
                  await wait(800);

                  const dateInput = document.querySelector(`${cssPath} input`) ||
                                   document.querySelector(`${cssPath} .el-input__inner`);

                  if (dateInput && dateInput.value && dateInput.value.trim() !== '') {
                    Statistics.addLog(`✅ ${fieldName}通过备用按钮设置成功: ${dateInput.value}`);
                    document.body.click();
                    await wait(300);
                    return true;
                  }
                }
              }
            } catch (e) {
              // 忽略选择器错误
            }
          }

          // 如果找不到"此刻"按钮，可能需要重新点击输入框
          if (attempt <= 2) {
            Statistics.addLog(`🔄 重新点击${fieldName}输入框`);
            dateInput.click();
            await wait(1000);
          }
        }

      } catch (setError) {
        Statistics.addLog(`❌ ${fieldName}第${attempt}次操作失败: ${setError.message}`);
      }

      if (attempt < 5) {
        await wait(500);
      }
    }

    Statistics.addLog(`❌ ${fieldName}5次尝试均失败`);

    // 确保关闭任何打开的日期选择器
    try {
      document.body.click();
    } catch (e) {}

    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 根据标签文本查找表单项
async function findFormItemByLabel(labelText) {
  const labels = document.querySelectorAll('label.el-form-item__label');
  for (const label of labels) {
    if (label.textContent.trim() === labelText) {
      return label.closest('.el-form-item');
    }
  }
  return null;
}

// 强制选择特定选项
async function forceSelectSpecificOption(fieldName, selectBox, fieldConfig) {
  try {
    // 快速关闭所有打开的下拉框
    const openDropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
    for (const dropdown of openDropdowns) {
      dropdown.style.display = 'none';
    }

    // 特殊处理线索等级字段（无需额外等待）
    if (fieldName === '线索等级') {
      await wait(10); // 最小等待确保下拉框关闭
      return await handleLevelSelect(selectBox);
    }

    await wait(300); // 其他字段保持原有等待时间

    // 点击选择框
    selectBox.click();
    await wait(getOperationWaitTime());

    // 等待下拉框出现（优化版）
    let dropdown = null;
    for (let i = 0; i < 3; i++) { // 减少重试次数
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
      if (dropdowns.length > 0) {
        dropdown = dropdowns[dropdowns.length - 1]; // 取最后一个（最新打开的）
        break;
      }
      await wait(getOperationWaitTime());
    }

    if (!dropdown) {
      Statistics.addLog(`${fieldName} 下拉框未出现`);
      return false;
    }

    const options = dropdown.querySelectorAll('.el-select-dropdown__item');
    Statistics.addLog(`${fieldName} 找到 ${options.length} 个选项`);

    // 查找匹配的选项
    let selectedOption = null;

    // 首先尝试默认选项
    if (fieldConfig.defaultOption) {
      selectedOption = Array.from(options).find(opt =>
        opt.textContent.trim() === fieldConfig.defaultOption
      );
    }

    // 如果没找到默认选项，尝试其他预期选项
    if (!selectedOption && fieldConfig.expectedOptions) {
      for (const expectedOption of fieldConfig.expectedOptions) {
        selectedOption = Array.from(options).find(opt =>
          opt.textContent.trim() === expectedOption
        );
        if (selectedOption) break;
      }
    }

    if (selectedOption) {
      selectedOption.click();
      await wait(getOperationWaitTime());

      // 确保下拉框关闭
      document.body.click();
      await wait(getOperationWaitTime());

      Statistics.addLog(`✅ ${fieldName}设置成功`);
      return true;
    } else {
      // 关闭下拉框
      document.body.click();
      await wait(100); // 减少等待时间
      return false;
    }

  } catch (error) {
    Statistics.addLog(`${fieldName} 选择失败: ${error.message}`, 'error');
    try {
      document.body.click();
    } catch (e) {}
    return false;
  }
}

// 简化的线索等级选择处理
async function handleLevelSelect(selectBox) {
  try {
    Statistics.addLog(`🎯 处理线索等级字段`);

    // 直接点击选择框
    selectBox.click();

    // 快速等待下拉框出现
    await fastWaitForVisible('.el-select-dropdown:not([style*="display: none"])');

    // 查找可见的下拉框
    const visibleDropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"])');
    if (!visibleDropdown) {
      Statistics.addLog('线索等级下拉框未出现', 'warning');
      return false;
    }

    const options = visibleDropdown.querySelectorAll('.el-select-dropdown__item');
    if (options.length === 0) {
      Statistics.addLog('线索等级无可用选项', 'warning');
      return false;
    }

    // 快速查找合适的选项（优先B级30天选项，符合业务逻辑）
    let selectedOption = Array.from(options).find(opt => {
      const text = opt.textContent.trim();
      return text.includes('B') && text.includes('30天');
    });

    // 如果没找到B级30天，查找任何包含30天的选项
    if (!selectedOption) {
      selectedOption = Array.from(options).find(opt => {
        const text = opt.textContent.trim();
        return text.includes('30天');
      });
    }

    // 如果还没找到，选择第一个非空选项
    if (!selectedOption) {
      selectedOption = Array.from(options).find(opt => {
        const text = opt.textContent.trim();
        return text !== '' && text !== '请选择' && text !== '全部';
      });
    }

    if (selectedOption) {
      selectedOption.click();
      await wait(10); // 最小等待

      Statistics.addLog(`✅ 线索等级设置成功`);
      return true;
    }

    Statistics.addLog(`❌ 线索等级未找到合适选项`);
    return false;

  } catch (error) {
    Statistics.addLog(`线索等级处理失败: ${error.message}`, 'error');
    return false;
  }
}



// 强制填充日期
async function forceFillDate(fieldName, dateEditor) {
  try {
    Statistics.addLog(`📅 开始填充日期字段: ${fieldName}`);

    const input = dateEditor.querySelector('input');
    if (!input) {
      Statistics.addLog(`❌ ${fieldName} 未找到日期输入框`);
      return false;
    }

    // 检查是否已有值
    if (input.value && input.value.trim() !== '') {
      Statistics.addLog(`📅 ${fieldName} 已有值: ${input.value}，强制重新填充`);
    }

    // 滚动到位置
    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(400);

    // 多次尝试激活输入框
    for (let i = 0; i < 3; i++) {
      input.click();
      await wait(300);
      input.focus();
      await wait(200);
    }

    let dateString = '';
    if (fieldName === '预购日期') {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
      futureDate.setHours(10 + Math.floor(Math.random() * 8));
      futureDate.setMinutes(Math.floor(Math.random() * 60));

      const year = futureDate.getFullYear();
      const month = String(futureDate.getMonth() + 1).padStart(2, '0');
      const day = String(futureDate.getDate()).padStart(2, '0');
      const hour = String(futureDate.getHours()).padStart(2, '0');
      const minute = String(futureDate.getMinutes()).padStart(2, '0');
      dateString = `${year}-${month}-${day} ${hour}:${minute}`;
    } else if (fieldName === '计划跟进时间') {
      const followDate = new Date();
      followDate.setDate(followDate.getDate() + Math.floor(Math.random() * 3) + 1);
      followDate.setHours(9 + Math.floor(Math.random() * 9));
      followDate.setMinutes(Math.floor(Math.random() * 60));

      const year = followDate.getFullYear();
      const month = String(followDate.getMonth() + 1).padStart(2, '0');
      const day = String(followDate.getDate()).padStart(2, '0');
      const hour = String(followDate.getHours()).padStart(2, '0');
      const minute = String(followDate.getMinutes()).padStart(2, '0');
      dateString = `${year}-${month}-${day} ${hour}:${minute}`;
    }

    if (dateString) {
      Statistics.addLog(`📅 准备设置日期: ${dateString}`);

      // 多种方法尝试设置值
      try {
        // 方法1: 直接设置值
        input.value = '';
        await wait(100);
        input.value = dateString;

        // 方法2: 模拟用户输入
        input.dispatchEvent(new Event('focus', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('input', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        // 方法3: 触发Vue事件（如果是Vue组件）
        if (input.__vue__) {
          input.__vue__.$emit('input', dateString);
          input.__vue__.$emit('change', dateString);
        }

        await wait(300);

        // 验证是否设置成功
        if (input.value === dateString) {
          Statistics.addLog(`✅ ${fieldName} 日期设置成功: ${dateString}`);
          return true;
        } else {
          Statistics.addLog(`⚠️ ${fieldName} 日期设置后验证失败，当前值: ${input.value}`);

          // 再次尝试设置
          input.value = dateString;
          input.dispatchEvent(new Event('input', { bubbles: true }));
          input.dispatchEvent(new Event('change', { bubbles: true }));
          await wait(200);

          if (input.value === dateString) {
            Statistics.addLog(`✅ ${fieldName} 第二次尝试成功: ${dateString}`);
            return true;
          }
        }

      } catch (setError) {
        Statistics.addLog(`❌ ${fieldName} 设置日期时出错: ${setError.message}`);
      }
    }

    Statistics.addLog(`❌ ${fieldName} 日期填充失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ 填充日期${fieldName}失败: ${error.message}`, 'error');
    return false;
  }
}

// 填充日期字段
async function fillDateFields() {
  try {
    Statistics.addLog('📅 开始填充日期字段');

    // 查找所有日期输入框
    const dateFields = document.querySelectorAll('.el-date-editor input');
    Statistics.addLog(`📅 找到 ${dateFields.length} 个日期字段`);

    for (const dateField of dateFields) {
      if (dateField.value && dateField.value.trim() !== '') {
        Statistics.addLog(`📅 日期字段已有值，跳过: ${dateField.value}`);
        continue;
      }

      const formItem = dateField.closest('.el-form-item');
      const label = formItem?.querySelector('label');
      const fieldName = label?.textContent.trim();

      if (!fieldName) {
        Statistics.addLog('📅 未找到日期字段标签');
        continue;
      }

      // 滚动到字段位置
      dateField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      await wait(300);

      if (fieldName === '预购日期') {
        // 设置为未来7-30天的随机日期
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
        futureDate.setHours(10 + Math.floor(Math.random() * 8)); // 10-17点
        futureDate.setMinutes(Math.floor(Math.random() * 60));

        const year = futureDate.getFullYear();
        const month = String(futureDate.getMonth() + 1).padStart(2, '0');
        const day = String(futureDate.getDate()).padStart(2, '0');
        const hour = String(futureDate.getHours()).padStart(2, '0');
        const minute = String(futureDate.getMinutes()).padStart(2, '0');
        const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

        // 点击输入框激活
        dateField.click();
        await wait(200);

        // 设置值
        dateField.value = dateString;
        dateField.dispatchEvent(new Event('input', { bubbles: true }));
        dateField.dispatchEvent(new Event('change', { bubbles: true }));
        dateField.dispatchEvent(new Event('blur', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${dateString}`);
        await wait(300);

      } else if (fieldName === '计划跟进时间') {
        // 设置为未来1-3天的随机时间
        const followDate = new Date();
        followDate.setDate(followDate.getDate() + Math.floor(Math.random() * 3) + 1);
        followDate.setHours(9 + Math.floor(Math.random() * 9)); // 9-17点
        followDate.setMinutes(Math.floor(Math.random() * 60));

        const year = followDate.getFullYear();
        const month = String(followDate.getMonth() + 1).padStart(2, '0');
        const day = String(followDate.getDate()).padStart(2, '0');
        const hour = String(followDate.getHours()).padStart(2, '0');
        const minute = String(followDate.getMinutes()).padStart(2, '0');
        const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

        // 点击输入框激活
        dateField.click();
        await wait(200);

        // 设置值
        dateField.value = dateString;
        dateField.dispatchEvent(new Event('input', { bubbles: true }));
        dateField.dispatchEvent(new Event('change', { bubbles: true }));
        dateField.dispatchEvent(new Event('blur', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${dateString}`);
        await wait(300);
      }
    }
  } catch (error) {
    Statistics.addLog(`填充日期字段失败: ${error.message}`, 'error');
  }
}

// 填充文本区域
async function fillTextAreas() {
  try {
    const textAreas = document.querySelectorAll('textarea');

    for (const textArea of textAreas) {
      if (textArea.value) continue; // 跳过已有值的字段

      const label = textArea.closest('.el-form-item')?.querySelector('label');
      const fieldName = label?.textContent.trim();

      if (fieldName === '跟进说明') {
        const remarks = [
          '客户对车型表现出浓厚兴趣，计划近期到店详细了解',
          '已向客户介绍车型基本信息，客户反馈良好',
          '客户询问了优惠政策和金融方案，需要进一步跟进',
          '客户表示需要和家人商量，约定下次联系时间',
          '已发送车型详细资料给客户，等待客户反馈'
        ];

        const randomRemark = remarks[Math.floor(Math.random() * remarks.length)];
        textArea.value = randomRemark;
        textArea.dispatchEvent(new Event('input', { bubbles: true }));
        textArea.dispatchEvent(new Event('change', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${randomRemark}`);
        await wait(200);
      }
    }
  } catch (error) {
    Statistics.addLog(`填充文本区域失败: ${error.message}`, 'error');
  }
}

// 处理车型选择框
async function handleCarTypeSelect() {
  return handleSelect('车型', "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(2) > div:nth-child(1) > div > div > div > div.el-input.el-input--small.el-input--suffix");
}

// 处理真实性选择框
async function handleRealitySelect() {
  return handleSelect('真实性', "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(4) > div:nth-child(4) > div > div > div > div.el-input.el-input--small.el-input--suffix");
}

// 专门处理Element UI日期选择器的函数
async function handleElementUIDatePicker(cssPath, fieldName) {
  try {
    const dateEditor = document.querySelector(cssPath);
    if (!dateEditor) {
      return false;
    }

    // 查找输入框
    const dateInput = dateEditor.querySelector('input');
    if (!dateInput) {
      return false;
    }

    // 首先尝试多种方式打开日期选择器
    const pickerOpened = await tryOpenDatePicker(dateEditor, dateInput, fieldName);
    if (!pickerOpened) {
      Statistics.addLog(`❌ ${fieldName}无法打开日期选择器，尝试简单设置`);
      return await trySimpleDateSet(dateInput, fieldName);
    }

    // 方法1: 尝试通过"此刻"按钮设置（最可靠）
    const nowButtonSuccess = await tryNowButton(dateInput, fieldName);
    if (nowButtonSuccess) {
      return true;
    }

    // 方法2: 尝试通过日期选择器面板设置
    const panelSuccess = await tryDatePickerPanel(dateInput, fieldName);
    if (panelSuccess) {
      return true;
    }

    // 方法3: 尝试简单设置（最后手段）
    const simpleSuccess = await trySimpleDateSet(dateInput, fieldName);
    if (simpleSuccess) {
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}所有方法均失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 尝试通过"此刻"按钮设置日期
async function tryNowButton(dateInput, fieldName) {
  try {


    // 点击输入框打开日期选择器
    dateInput.click();
    await wait(getOperationWaitTime());

    // 查找"此刻"按钮
    const nowButtons = document.querySelectorAll('.el-picker-panel__footer button');

    if (nowButtons.length > 0) {
      const nowButton = Array.from(nowButtons).find(btn =>
        btn.textContent.includes('此刻') || btn.textContent.includes('现在')
      );

      if (nowButton) {
        nowButton.click();
        await wait(getOperationWaitTime());

        // 验证设置结果
        if (dateInput.value && dateInput.value.trim() !== '') {
          Statistics.addLog(`✅ ${fieldName}设置成功: ${dateInput.value}`);

          // 关闭日期选择器
          document.body.click();
          await wait(getOperationWaitTime());

          return true;
        }
      }
    }

    Statistics.addLog(`❌ ${fieldName}"此刻"按钮方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}"此刻"按钮方法出错: ${error.message}`);
    return false;
  }
}

// 尝试通过日期选择器面板设置
async function tryDatePickerPanel(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 方法2: 尝试通过日期选择器面板设置${fieldName}`);

    // 点击输入框打开日期选择器
    dateInput.click();
    await wait(getOperationWaitTime() * 2);

    // 查找日期选择器面板
    const panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length === 0) {
      Statistics.addLog(`❌ ${fieldName}日期选择器面板未打开`);
      return false;
    }

    Statistics.addLog(`✅ 找到 ${panels.length} 个日期选择器面板`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);

    // 尝试点击对应的日期
    const targetDay = futureDate.getDate();
    const dayButtons = document.querySelectorAll('.el-date-table td .cell');

    for (const dayButton of dayButtons) {
      if (dayButton.textContent.trim() === String(targetDay)) {
        Statistics.addLog(`📅 点击日期: ${targetDay}号`);
        dayButton.click();
        await wait(500);

        // 如果是日期时间选择器，还需要设置时间
        const timeInputs = document.querySelectorAll('.el-time-spinner input');
        if (timeInputs.length >= 2) {
          // 设置小时
          timeInputs[0].value = '14';
          timeInputs[0].dispatchEvent(new Event('input', { bubbles: true }));

          // 设置分钟
          timeInputs[1].value = '30';
          timeInputs[1].dispatchEvent(new Event('input', { bubbles: true }));

          await wait(300);
        }

        // 点击确定按钮
        const confirmButtons = document.querySelectorAll('.el-picker-panel__footer button');
        const confirmButton = Array.from(confirmButtons).find(btn =>
          btn.textContent.includes('确定') || btn.textContent.includes('确认')
        );

        if (confirmButton) {
          confirmButton.click();
          await wait(500);

          // 验证设置结果
          if (dateInput.value && dateInput.value.trim() !== '') {
            Statistics.addLog(`✅ ${fieldName}通过日期面板设置成功: ${dateInput.value}`);
            return true;
          }
        }

        break;
      }
    }

    Statistics.addLog(`❌ ${fieldName}日期面板方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}日期面板方法出错: ${error.message}`);
    return false;
  }
}

// 尝试键盘输入
async function tryKeyboardInput(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 方法3: 尝试键盘输入设置${fieldName}`);

    // 生成未来日期字符串
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(14);
    futureDate.setMinutes(30);

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 准备键盘输入日期: ${dateString}`);

    // 聚焦输入框
    dateInput.focus();
    await wait(200);

    // 清空输入框
    dateInput.select();
    await wait(100);

    // 模拟键盘输入（添加超时保护）
    dateInput.value = '';
    let charIndex = 0;
    for (const char of dateString) {
      if (charIndex > 20) { // 防止无限循环
        Statistics.addLog(`⚠️ 键盘输入超过20个字符，停止输入`);
        break;
      }

      dateInput.value += char;
      dateInput.dispatchEvent(new KeyboardEvent('keydown', { key: char, bubbles: true }));
      dateInput.dispatchEvent(new KeyboardEvent('keypress', { key: char, bubbles: true }));
      dateInput.dispatchEvent(new Event('input', { bubbles: true }));
      dateInput.dispatchEvent(new KeyboardEvent('keyup', { key: char, bubbles: true }));
      await wait(getOperationWaitTime() / 2);
      charIndex++;
    }

    // 触发change和blur事件
    dateInput.dispatchEvent(new Event('change', { bubbles: true }));
    await wait(100);
    dateInput.blur();
    dateInput.dispatchEvent(new Event('blur', { bubbles: true }));
    await wait(500);

    // 验证设置结果
    if (dateInput.value && dateInput.value.includes(dateString.split(' ')[0])) {
      Statistics.addLog(`✅ ${fieldName}通过键盘输入设置成功: ${dateInput.value}`);
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}键盘输入方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}键盘输入方法出错: ${error.message}`);
    return false;
  }
}

// 尝试打开日期选择器
async function tryOpenDatePicker(dateEditor, dateInput, fieldName) {
  try {

    // 方法1: 点击输入框
    dateInput.click();
    await wait(getOperationWaitTime());

    let panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    // 方法2: 点击日期编辑器容器
    dateEditor.click();
    await wait(getOperationWaitTime());

    panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    // 方法3: 点击日期图标
    const dateIcon = dateEditor.querySelector('.el-input__icon, .el-icon-time, .el-icon-date');
    if (dateIcon) {
      dateIcon.click();
      await wait(getOperationWaitTime());

      panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
      if (panels.length > 0) {
        return true;
      }
    }

    // 方法4: 双击输入框
    dateInput.dispatchEvent(new MouseEvent('dblclick', { bubbles: true }));
    await wait(getOperationWaitTime());

    panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}所有打开方法均失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}打开日期选择器出错: ${error.message}`);
    return false;
  }
}

// 尝试简单设置日期（不依赖日期选择器）
async function trySimpleDateSet(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 尝试简单设置${fieldName}（不打开选择器）`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(14);
    futureDate.setMinutes(30);

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 设置日期为: ${dateString}`);

    // 聚焦并清空
    dateInput.focus();
    await wait(200);
    dateInput.select();
    await wait(100);

    // 直接设置值
    dateInput.value = dateString;

    // 触发所有可能的事件
    const events = ['focus', 'input', 'change', 'blur'];
    for (const eventType of events) {
      dateInput.dispatchEvent(new Event(eventType, { bubbles: true }));
      await wait(100);
    }

    // 模拟回车键确认
    dateInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
    dateInput.dispatchEvent(new KeyboardEvent('keyup', { key: 'Enter', bubbles: true }));

    await wait(500);

    // 验证设置结果
    if (dateInput.value && (dateInput.value === dateString || dateInput.value.includes(dateString.split(' ')[0]))) {
      Statistics.addLog(`✅ ${fieldName}简单设置成功: ${dateInput.value}`);
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}简单设置失败，当前值: ${dateInput.value}`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}简单设置出错: ${error.message}`);
    return false;
  }
}
