// 测试修复后的代码
console.log('开始测试修复...');

// 测试1: 验证所有类是否可以正确实例化
try {
  console.log('测试1: 类实例化');
  
  // 测试Logger
  const testLogger = new Logger();
  console.log('✅ Logger 实例化成功');
  
  // 测试ElementCache
  const testElementCache = new ElementCache();
  console.log('✅ ElementCache 实例化成功');
  
  // 测试MemoryManager
  const testMemoryManager = new MemoryManager();
  console.log('✅ MemoryManager 实例化成功');
  
  // 测试AutomationStateMachine
  const testStateMachine = new AutomationStateMachine();
  console.log('✅ AutomationStateMachine 实例化成功');
  
  // 测试ProgressIndicator
  const testProgressIndicator = new ProgressIndicator();
  console.log('✅ ProgressIndicator 实例化成功');
  
  // 测试DebugMode
  const testDebugMode = new DebugMode();
  console.log('✅ DebugMode 实例化成功');
  
  // 测试PerformanceMonitor
  const testPerformanceMonitor = new PerformanceMonitor();
  console.log('✅ PerformanceMonitor 实例化成功');
  
} catch (error) {
  console.error('❌ 类实例化测试失败:', error);
}

// 测试2: 验证状态机基本功能
try {
  console.log('\n测试2: 状态机功能');
  
  const testStateMachine = new AutomationStateMachine();
  
  // 测试状态转换
  testStateMachine.transition(testStateMachine.states.FINDING_BUTTON);
  console.log('✅ 状态转换成功');
  
  // 测试状态获取
  const currentState = testStateMachine.getCurrentState();
  console.log('✅ 状态获取成功:', currentState);
  
} catch (error) {
  console.error('❌ 状态机测试失败:', error);
}

// 测试3: 验证配置系统
try {
  console.log('\n测试3: 配置系统');
  
  if (typeof CONFIG !== 'undefined') {
    console.log('✅ CONFIG 对象存在');
    console.log('✅ 选择器配置:', Object.keys(CONFIG.selectors).length, '个');
    console.log('✅ 超时配置:', Object.keys(CONFIG.timeouts).length, '个');
    console.log('✅ 重试配置:', Object.keys(CONFIG.retryLimits).length, '个');
  } else {
    console.error('❌ CONFIG 对象不存在');
  }
  
} catch (error) {
  console.error('❌ 配置系统测试失败:', error);
}

// 测试4: 验证错误处理系统
try {
  console.log('\n测试4: 错误处理系统');
  
  if (typeof ErrorHandler !== 'undefined') {
    console.log('✅ ErrorHandler 存在');
    
    // 测试错误分类
    const testError = new Error('test error');
    const errorType = ErrorHandler.classifyError(testError);
    console.log('✅ 错误分类成功:', errorType);
    
    // 测试重试策略
    const strategy = ErrorHandler.getRetryStrategy(errorType);
    console.log('✅ 重试策略获取成功:', strategy);
  } else {
    console.error('❌ ErrorHandler 不存在');
  }
  
} catch (error) {
  console.error('❌ 错误处理系统测试失败:', error);
}

// 测试5: 验证工具函数
try {
  console.log('\n测试5: 工具函数');
  
  // 测试wait函数
  if (typeof wait === 'function') {
    console.log('✅ wait 函数存在');
  } else {
    console.error('❌ wait 函数不存在');
  }
  
  // 测试getRandomMessage函数
  if (typeof getRandomMessage === 'function') {
    console.log('✅ getRandomMessage 函数存在');
  } else {
    console.error('❌ getRandomMessage 函数不存在');
  }
  
  // 测试findFollowButton函数
  if (typeof findFollowButton === 'function') {
    console.log('✅ findFollowButton 函数存在');
  } else {
    console.error('❌ findFollowButton 函数不存在');
  }
  
} catch (error) {
  console.error('❌ 工具函数测试失败:', error);
}

console.log('\n测试完成！');
